import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Calendar, Clock, Eye, ExternalLink, Heart, Share2, Star } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface WechatArticleCardProps {
  article: any;
  getLocalizedData: (item: any, field: string) => string;
  variant?: 'default' | 'compact' | 'featured';
}

const WechatArticleCard: React.FC<WechatArticleCardProps> = ({ 
  article, 
  getLocalizedData,
  variant = 'default'
}) => {
  const { t } = useTranslation();

  const handleExternalLink = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(article.wechatUrl, '_blank');
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: getLocalizedData(article, 'title'),
        text: getLocalizedData(article, 'excerpt'),
        url: window.location.origin + `/wechat/article/${article.id}`
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(
        window.location.origin + `/wechat/article/${article.id}`
      );
    }
  };

  if (variant === 'compact') {
    return (
      <Card className="overflow-hidden hover:shadow-md transition-shadow duration-200">
        <div className="flex">
          <div className="w-24 h-24 flex-shrink-0">
            <img
              src={article.coverImage}
              alt={getLocalizedData(article, 'title')}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 p-4">
            <h4 className="font-medium text-sm line-clamp-2 mb-2">
              <Link 
                to={`/wechat/article/${article.id}`}
                className="hover:text-red-600 transition-colors"
              >
                {getLocalizedData(article, 'title')}
              </Link>
            </h4>
            <div className="flex items-center text-xs text-gray-500 space-x-3">
              <span className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                {article.publishDate}
              </span>
              <span className="flex items-center">
                <Eye className="w-3 h-3 mr-1" />
                {article.views}
              </span>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  if (variant === 'featured') {
    return (
      <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 border-red-200">
        <div className="relative">
          <div className="h-64 overflow-hidden">
            <img
              src={article.coverImage}
              alt={getLocalizedData(article, 'title')}
              className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
            />
          </div>
          <div className="absolute top-4 left-4 flex gap-2">
            <Badge className="bg-red-600 text-white">
              <Star className="w-3 h-3 mr-1" />
              {t('wechat.featured')}
            </Badge>
          </div>
          <div className="absolute top-4 right-4">
            <Button
              size="sm"
              variant="secondary"
              onClick={handleExternalLink}
              className="bg-white/90 hover:bg-white"
            >
              <ExternalLink className="w-3 h-3" />
            </Button>
          </div>
        </div>

        <CardHeader className="pb-2">
          <h2 className="font-bold text-xl line-clamp-2 hover:text-red-600 transition-colors">
            <Link to={`/wechat/article/${article.id}`}>
              {getLocalizedData(article, 'title')}
            </Link>
          </h2>
        </CardHeader>

        <CardContent>
          <p className="text-gray-600 line-clamp-3 mb-4">
            {getLocalizedData(article, 'excerpt')}
          </p>

          {/* 文章信息 */}
          <div className="flex items-center text-sm text-gray-500 mb-4 space-x-4">
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              {article.publishDate}
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              {getLocalizedData(article, 'readTime')}
            </div>
            <div className="flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              {article.views}
            </div>
          </div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-2 mb-4">
            {getLocalizedData(article, 'tags').slice(0, 3).map((tag: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>

          {/* 按钮组 */}
          <div className="flex gap-2">
            <Button asChild className="flex-1 bg-red-600 hover:bg-red-700">
              <Link to={`/wechat/article/${article.id}`}>
                {t('wechat.readMore')}
              </Link>
            </Button>
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200 group">
      {/* 封面图片 */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={article.coverImage}
          alt={getLocalizedData(article, 'title')}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        />
        {article.featured && (
          <Badge className="absolute top-2 left-2 bg-red-600 text-white">
            <Star className="w-3 h-3 mr-1" />
            {t('wechat.featured')}
          </Badge>
        )}
        <div className="absolute top-2 right-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={handleExternalLink}
            className="bg-white/80 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <ExternalLink className="w-3 h-3" />
          </Button>
        </div>
      </div>

      <CardHeader className="pb-2">
        <h3 className="font-semibold text-lg line-clamp-2 hover:text-red-600 transition-colors">
          <Link to={`/wechat/article/${article.id}`}>
            {getLocalizedData(article, 'title')}
          </Link>
        </h3>
      </CardHeader>

      <CardContent>
        <p className="text-gray-600 text-sm line-clamp-3 mb-4">
          {getLocalizedData(article, 'excerpt')}
        </p>

        {/* 文章信息 */}
        <div className="flex items-center text-xs text-gray-500 mb-4 space-x-3">
          <div className="flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            {article.publishDate}
          </div>
          <div className="flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            {getLocalizedData(article, 'readTime')}
          </div>
          <div className="flex items-center">
            <Eye className="w-3 h-3 mr-1" />
            {article.views}
          </div>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-1 mb-4">
          {getLocalizedData(article, 'tags').slice(0, 3).map((tag: string, index: number) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        {/* 互动数据 */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <Heart className="w-3 h-3 mr-1" />
              {article.likes}
            </span>
            <span className="flex items-center">
              <Share2 className="w-3 h-3 mr-1" />
              {article.shares}
            </span>
          </div>
          <span className="text-gray-400">
            {getLocalizedData(article, 'author')}
          </span>
        </div>

        {/* 按钮组 */}
        <div className="flex gap-2">
          <Button asChild size="sm" className="flex-1 bg-red-600 hover:bg-red-700">
            <Link to={`/wechat/article/${article.id}`}>
              {t('wechat.readMore')}
            </Link>
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share2 className="w-3 h-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default WechatArticleCard;
