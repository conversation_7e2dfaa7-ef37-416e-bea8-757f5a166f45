
import LanguageSwitcherSidebar from './LanguageSwitcherSiderbar';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Building2,
  Newspaper,
  Stethoscope,
  Heart,
  BookOpen,
  Handshake,
  Phone,
  Award,
  GraduationCap,
  MessageSquare,
  ChevronDown,
  Search,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

interface SidebarProps {
  onClose?: () => void;
  collapsed?: boolean;
}

const Sidebar = ({ onClose, collapsed = false }: SidebarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const {t} = useTranslation();
  const [openItems, setOpenItems] = useState<{ [key: string]: boolean }>({});

  const toggleItem = (itemName: string) => {
    setOpenItems((prev) => ({
      ...prev,
      [itemName]: !prev[itemName],
    }));
  };

  // Don't render if collapsed on desktop
  if (collapsed && !isMobile) {
    return null;
  }

  const navigation = [
    { name: t('Sidebar.navigation.groupIntroduction'), href: '/hospital', icon: Building2,
      children: [
        { name: t('Sidebar.navigation.hospitalOverview'), href: '/hospital/overview' },
        { name: t('Sidebar.navigation.hospitalCulture'), href: '/hospital/culture' },
        { name: t('Sidebar.navigation.hospitalHistory'), href: '/hospital/history' },
        { name: t('Sidebar.navigation.socialCooperation'), href: '/hospital/social-cooperation' },
      ]
    },
    {
      name: t('Sidebar.navigation.newsCenter'),
      icon: Newspaper,
      children: [
        { name: t('Sidebar.navigation.hospitalNews'), href: '/news' },
        { name: t('Sidebar.navigation.hospitalStyle'), href: '/news/gallery' },
        { name: t('Sidebar.navigation.videoDisplay'), href: '/news/videos' },
      ]
    },
    { name: t('Sidebar.navigation.expertDoctors'), href: '/doctors', icon: Stethoscope,
      children: [
        { name: t('Sidebar.navigation.internalMedicine'), href: '/doctors/internal-medicine' },
        { name: t('Sidebar.navigation.surgery'), href: '/doctors/surgery' },
        { name: t('Sidebar.navigation.dermatology'), href: '/doctors/dermatology' },
        { name: t('Sidebar.navigation.gynecology'), href: '/doctors/gynecology' },
        { name: t('Sidebar.navigation.pediatrics'), href: '/doctors/pediatrics' },
        { name: t('Sidebar.navigation.entOphthalmology'), href: '/doctors/ent-ophthalmology' },
        { name: t('Sidebar.navigation.acupunctureMassage'), href: '/doctors/acupuncture-massage' },
        { name: t('Sidebar.navigation.ultrasound'), href: '/doctors/ultrasound' },
        { name: t('Sidebar.navigation.proctology'), href: '/doctors/proctology' },
        { name: t('Sidebar.navigation.tcmRehabilitation'), href: '/doctors/tcm-rehabilitation' },
      ]
    },
     {
      name: t('Sidebar.navigation.healthManagement'),
      icon: Heart,
      children: [
        { name: t('Sidebar.navigation.healthPackages'), href: '/health/packages' },
        { name: t('Sidebar.navigation.healthCare'), href: '/health/care' },
      ]
    },
     {
      name: t('Sidebar.navigation.scienceTherapy'),
      icon: BookOpen,
      children: [
        { name: t('Sidebar.navigation.tcmScience'), href: '/therapy/tcm-science' },
        { name: t('Sidebar.navigation.specialTherapy'), href: '/therapy/special' },
      ]
    },
    { name: t('Sidebar.navigation.wechatArticles'), href: '/wechat', icon: MessageSquare },
    { name: t('Sidebar.navigation.exportBase'), href: '/special/export-base', icon: Award },
    { name: t('Sidebar.navigation.educationBase'), href: '/special/education-base', icon: GraduationCap },
    { name: t('Sidebar.navigation.cooperationCases'), href: '/cooperation', icon: Handshake,
      children: [
        { name: t('Sidebar.navigation.domesticCooperation'), href: '/cooperation/domestic' },
        { name: t('Sidebar.navigation.internationalCooperation'), href: '/cooperation/international' },
      ]
    },
    { name: t('Sidebar.navigation.contactUs'), href: '/contact', icon: Phone,
      children: [
        { name: t('Sidebar.navigation.contactInfo'), href: '/contact' },
        { name: t('Sidebar.navigation.onlineMessage'), href: '/contact/email' },
      ]
    },
  ];

  // const languages = ['CN', 'EN', 'RU'];

  const handleLinkClick = () => {
    if (isMobile && onClose) {
      onClose();
    }
  };

  return (
    <div className={`
      ${isMobile ? 'w-80 h-full' : 'w-80 h-screen'}
      bg-white shadow-lg border-r border-gray-200 flex flex-col
      ${isMobile ? 'fixed' : 'relative'}
    `}>
      {/* Logo */}
      <div className="p-4 md:p-6 bg-red-600 relative flex-shrink-0">
        {isMobile && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:bg-red-700 rounded p-1 z-10"
          >
            <X className="w-5 h-5" />
          </button>
        )}
        <div className="text-white pr-12 ml-[60px]">
          <h1 className="text-xl md:text-2xl font-bold mb-1">{t('common.logo.main')}</h1>
        </div>
      </div>

      {/* Search */}
      <div className="p-3 md:p-4 border-b border-gray-200 flex-shrink-0">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder={t("Sidebar.placeholder")}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Language Selector */}
      <div className="p-3 md:p-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex flex-wrap gap-2">
          <LanguageSwitcherSidebar/>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-3 md:p-4 sidebar-scroll scroll-smooth">
        <ul className="space-y-1 pb-6">
          {navigation.map((item) => (
            <li key={item.name}>
              {item.children ? (
                <div>
                  <div
                    onClick={() => {
                      // 同时执行跳转和展开/收起操作
                      toggleItem(item.name);
                      // 使用 React Router 导航跳转
                      navigate(item.href);
                      // 如果是移动端，关闭侧边栏
                      if (isMobile && onClose) {
                        onClose();
                      }
                    }}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm font-medium rounded-md cursor-pointer transition-colors",
                      location.pathname === item.href
                        ? "bg-red-50 text-red-600"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                    title={item.name}
                  >
                    <item.icon className="w-4 h-4 mr-3 flex-shrink-0" />
                    <span className="flex-1 truncate">{item.name}</span>
                    <ChevronDown
                      className={`w-4 h-4 flex-shrink-0 transform transition-transform duration-200 ${
                        openItems[item.name] ? 'rotate-180' : ''
                      }`}
                    />
                  </div>
                   {openItems[item.name] && (
                  <ul className="ml-7 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <li key={child.name}>
                        <Link
                          to={child.href}
                          onClick={handleLinkClick}
                          title={child.name}
                          className={cn(
                            "block px-3 py-2 text-sm rounded-md transition-colors truncate",
                            location.pathname === child.href
                              ? "bg-red-50 text-red-600 font-medium"
                              : "text-gray-600 hover:bg-gray-100"
                          )}
                        >
                          {child.name}
                        </Link>
                      </li>
                    ))}
                  </ul>)}
                </div>
              ) : (
                <Link
                  to={item.href}
                  onClick={handleLinkClick}
                  title={item.name}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    location.pathname === item.href
                      ? "bg-red-50 text-red-600"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  <item.icon className="w-4 h-4 mr-3 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
