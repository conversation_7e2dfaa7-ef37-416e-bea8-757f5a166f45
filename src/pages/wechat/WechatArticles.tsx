import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import PageTemplate from '@/components/PageTemplate';
import CategoryTabs from '@/components/CategoryTabs';
import WechatArticleCard from '@/components/WechatArticleCard';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { getBreadcrumbs } from '@/routes';
import wechatData from '@/data/wechat-articles.json';

const WechatArticles: React.FC = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);
  
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const articlesPerPage = 9;

  // 获取当前语言的数据
  const getLocalizedData = (item: any, field: string) => {
    const lang = i18n.language;
    if (lang === 'en' && item[`${field}En`]) return item[`${field}En`];
    if (lang === 'ru' && item[`${field}Ru`]) return item[`${field}Ru`];
    return item[field];
  };

  // 处理分类数据
  const categories = useMemo(() => {
    return [
      { id: 'all', name: t('common.all'), color: 'gray' },
      ...wechatData.categories.map(cat => ({
        id: cat.id,
        name: getLocalizedData(cat, 'name'),
        color: cat.color
      }))
    ];
  }, [i18n.language, t]);

  // 筛选文章
  const filteredArticles = useMemo(() => {
    return wechatData.articles.filter(article => {
      const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
      const matchesSearch = searchTerm === '' || 
        getLocalizedData(article, 'title').toLowerCase().includes(searchTerm.toLowerCase()) ||
        getLocalizedData(article, 'excerpt').toLowerCase().includes(searchTerm.toLowerCase()) ||
        getLocalizedData(article, 'tags').some((tag: string) => 
          tag.toLowerCase().includes(searchTerm.toLowerCase())
        );
      return matchesCategory && matchesSearch;
    });
  }, [selectedCategory, searchTerm, i18n.language]);

  // 分页逻辑
  const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);
  const currentArticles = filteredArticles.slice(
    (currentPage - 1) * articlesPerPage,
    currentPage * articlesPerPage
  );

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 处理分类变化
  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setCurrentPage(1);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  return (
    <PageTemplate
      title={t('wechat.title')}
      subtitle={t('wechat.subtitle')}
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: t('wechat.title'),
        description: t('wechat.subtitle'),
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 分类筛选 */}
      <CategoryTabs
        categories={categories}
        activeCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        theme="red"
        showAllOption={false}
        className="mb-8"
      />

      {/* 搜索和统计 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div className="relative max-w-md w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t('wechat.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="text-sm text-gray-600">
          {t('wechat.articlesCount', { count: filteredArticles.length })}
        </div>
      </div>

      {/* 文章网格 */}
      {currentArticles.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {currentArticles.map(article => (
            <WechatArticleCard
              key={article.id}
              article={article}
              getLocalizedData={getLocalizedData}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg">{t('wechat.noArticlesFound')}</p>
            <p className="text-sm">{t('wechat.tryDifferentSearch')}</p>
          </div>
          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm('');
              setSelectedCategory('all');
              setCurrentPage(1);
            }}
          >
            {t('wechat.clearFilters')}
          </Button>
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex items-center space-x-2">
            {/* 上一页 */}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => handlePageChange(currentPage - 1)}
              className="px-3 py-2"
            >
              {t('common.previousPage')}
            </Button>

            {/* 页码 */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 ${
                  currentPage === page
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                {page}
              </Button>
            ))}

            {/* 下一页 */}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => handlePageChange(currentPage + 1)}
              className="px-3 py-2"
            >
              {t('common.nextPage')}
            </Button>
          </div>
        </div>
      )}

      {/* 精选文章提示 */}
      {selectedCategory === 'all' && searchTerm === '' && (
        <div className="mt-12 bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            {t('wechat.featuredArticles')}
          </h3>
          <p className="text-red-700 text-sm">
            {t('wechat.featuredDescription')}
          </p>
        </div>
      )}
    </PageTemplate>
  );
};

export default WechatArticles;
