import React, { useMemo } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Calendar, 
  Clock, 
  Eye, 
  Heart, 
  Share2, 
  ExternalLink, 
  ArrowLeft,
  User,
  Tag
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import WechatArticleCard from '@/components/WechatArticleCard';
import wechatData from '@/data/wechat-articles.json';

const WechatArticleDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();

  // 获取当前语言的数据
  const getLocalizedData = (item: any, field: string) => {
    const lang = i18n.language;
    if (lang === 'en' && item[`${field}En`]) return item[`${field}En`];
    if (lang === 'ru' && item[`${field}Ru`]) return item[`${field}Ru`];
    return item[field];
  };

  // 查找当前文章
  const article = useMemo(() => {
    return wechatData.articles.find(article => article.id === id);
  }, [id]);

  // 获取相关文章
  const relatedArticles = useMemo(() => {
    if (!article) return [];
    return wechatData.articles
      .filter(item => item.id !== article.id && item.category === article.category)
      .slice(0, 3);
  }, [article]);

  // 获取分类信息
  const categoryInfo = useMemo(() => {
    if (!article) return null;
    return wechatData.categories.find(cat => cat.id === article.category);
  }, [article]);

  if (!article) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {t('wechat.articleNotFound')}
          </h1>
          <p className="text-gray-600 mb-6">
            {t('wechat.articleNotFoundDescription')}
          </p>
          <Button onClick={() => navigate('/wechat')}>
            {t('wechat.backToList')}
          </Button>
        </div>
      </div>
    );
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: getLocalizedData(article, 'title'),
        text: getLocalizedData(article, 'excerpt'),
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 返回按钮 */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/wechat')}
            className="flex items-center text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('wechat.backToList')}
          </Button>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 文章头部 */}
        <article className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
          {/* 封面图片 */}
          <div className="relative h-64 md:h-80 overflow-hidden">
            <img
              src={article.coverImage}
              alt={getLocalizedData(article, 'title')}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="flex items-center gap-2 mb-2">
                {categoryInfo && (
                  <Badge className="bg-red-600 text-white">
                    {getLocalizedData(categoryInfo, 'name')}
                  </Badge>
                )}
                {article.featured && (
                  <Badge className="bg-yellow-500 text-white">
                    {t('wechat.featured')}
                  </Badge>
                )}
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
                {getLocalizedData(article, 'title')}
              </h1>
            </div>
          </div>

          {/* 文章信息 */}
          <div className="p-6">
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center">
                <User className="w-4 h-4 mr-1" />
                {getLocalizedData(article, 'author')}
              </div>
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {article.publishDate}
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {getLocalizedData(article, 'readTime')}
              </div>
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                {article.views}
              </div>
            </div>

            {/* 摘要 */}
            <p className="text-gray-700 text-lg leading-relaxed mb-6">
              {getLocalizedData(article, 'excerpt')}
            </p>

            {/* 标签 */}
            <div className="flex flex-wrap gap-2 mb-6">
              {getLocalizedData(article, 'tags').map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" className="flex items-center">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3 mb-6">
              <Button
                onClick={() => window.open(article.wechatUrl, '_blank')}
                className="bg-green-600 hover:bg-green-700"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                {t('wechat.viewOnWechat')}
              </Button>
              <Button variant="outline" onClick={handleShare}>
                <Share2 className="w-4 h-4 mr-2" />
                {t('wechat.shareArticle')}
              </Button>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="flex items-center">
                  <Heart className="w-4 h-4 mr-1" />
                  {article.likes}
                </span>
                <span className="flex items-center">
                  <Share2 className="w-4 h-4 mr-1" />
                  {article.shares}
                </span>
              </div>
            </div>

            {/* 文章内容 */}
            <div 
              className="prose prose-lg max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-a:text-red-600 prose-strong:text-gray-800"
              dangerouslySetInnerHTML={{ 
                __html: getLocalizedData(article, 'content') 
              }}
            />
          </div>
        </article>

        {/* 相关文章 */}
        {relatedArticles.length > 0 && (
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6">
                {t('wechat.relatedArticles')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedArticles.map(relatedArticle => (
                  <WechatArticleCard
                    key={relatedArticle.id}
                    article={relatedArticle}
                    getLocalizedData={getLocalizedData}
                    variant="compact"
                  />
                ))}
              </div>
              <div className="text-center mt-6">
                <Button asChild variant="outline">
                  <Link to="/wechat">
                    {t('wechat.viewAllArticles')}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default WechatArticleDetail;
